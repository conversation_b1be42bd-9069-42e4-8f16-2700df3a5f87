'use strict';

const express = require('express');
const Joi = require('joi');
const mobileLeaveController = require('../../controllers/mobile/leave.controller');
const { validate, validateQuery, validateParams } = require('../../middlewares/validation.middleware');
const mobileLeaveValidator = require('../../validators/mobile/leave.validator');
const authenticate = require('../../middlewares/authentication.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Mobile leave type routes
router.get(
  '/leave-types',
  validateQuery(mobileLeaveValidator.getLeaveTypes),
  mobileLeaveController.getLeaveTypes
);

// Mobile leave application routes
router.post(
  '/leaves',
  validate(mobileLeaveValidator.applyLeave),
  mobileLeaveController.applyLeave
);

router.get(
  '/leaves',
  validateQuery(mobileLeaveValidator.getLeaves),
  mobileLeaveController.getLeaves
);

router.get(
  '/employee-leave',
  validateQuery(mobileLeaveValidator.getLeaves),
  mobileLeaveController.getEmployeeLeaves
);

// Mobile leave details route
router.get(
  '/leaves/:id',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  mobileLeaveController.getLeaveDetails
);

// Mobile leave management routes with ID parameter
router.put(
  '/leaves/:leaveId',
  validateParams(Joi.object({
    leaveId: Joi.number().integer().positive().required()
  })),
  validate(mobileLeaveValidator.updateLeave),
  mobileLeaveController.updateLeave
);

router.delete(
  '/leaves/:leaveId',
  validateParams(Joi.object({
    leaveId: Joi.number().integer().positive().required()
  })),
  mobileLeaveController.withdrawLeave
);

// Mobile leave approval routes
router.put(
  '/leaves/approve-reject/:id',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  validate(mobileLeaveValidator.approveRejectLeave),
  mobileLeaveController.approveRejectLeave
);

// Mobile bulk leave approval route (v2)
router.put(
  '/v2/leaves/approve-reject',
  validate(mobileLeaveValidator.bulkApproveRejectLeaves),
  mobileLeaveController.bulkApproveRejectLeaves
);

module.exports = router;
