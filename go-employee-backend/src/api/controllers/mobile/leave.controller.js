'use strict';

const leaveService = require('../../../services/leave/leaveService');
const leaveBalanceService = require('../../../services/leave/leaveBalanceService');
const { mobileSuccessResponse, mobilePaginatedResponse } = require('../../../common/utils/response');
const logger = require('../../../common/logging');
const employeeService = require('../../../services/employee/employeeService');
const workflowInstanceService = require('../../../services/workflow/workflowInstanceService');
const LeaveWorkflowService = require('../../../services/leave/leaveWorkflowService');
const leaveWorkflowService = new LeaveWorkflowService();
const approvalDetailsService = require('../../../services/workflow/approvalDetailsService');
const { NotFoundError, ValidationError } = require('../../../common/errors');
const { Employee, LeaveType, WorkflowAssignment, WorkflowInstance, Leave } = require('../../../data/models');
const { Op } = require('sequelize');


/**
 * Mobile field transformation utilities
 */
const transformMobileToBackend = (mobileData) => {
  const backendData = {};
  // Map mobile field names to backend field names
  if (mobileData.leave_type_id !== undefined) backendData.leaveTypeId = mobileData.leave_type_id;

  // Handle both date field naming conventions
  if (mobileData.from_date !== undefined) backendData.startDate = mobileData.from_date;
  if (mobileData.to_date !== undefined) backendData.endDate = mobileData.to_date;
  if (mobileData.start_date !== undefined) backendData.startDate = mobileData.start_date;
  if (mobileData.end_date !== undefined) backendData.endDate = mobileData.end_date;

  if (mobileData.reason !== undefined) backendData.reason = mobileData.reason;
  if (mobileData.no_of_days !== undefined) backendData.totalDays = mobileData.no_of_days;

  // Handle is_half_day field directly
  if (mobileData.is_half_day !== undefined) {
    backendData.isHalfDay = mobileData.is_half_day;
    if (mobileData.is_half_day) {
      backendData.halfDayType = mobileData.half_day_type || 'first_half';
    } else {
      backendData.halfDayType = null;
    }
  }

  // Handle leave session (half day type) - alternative format
  if (mobileData.leave_session !== undefined) {
    if (mobileData.leave_session === 'Full Day') {
      backendData.isHalfDay = false;
      backendData.halfDayType = null;
    } else if (mobileData.leave_session === 'First Half') {
      backendData.isHalfDay = true;
      backendData.halfDayType = 'first_half';
    } else if (mobileData.leave_session === 'Second Half') {
      backendData.isHalfDay = true;
      backendData.halfDayType = 'second_half';
    }
  }
  
  // Handle time fields (for half day leaves)
  if (mobileData.from_time !== undefined) backendData.fromTime = mobileData.from_time;
  if (mobileData.to_time !== undefined) backendData.toTime = mobileData.to_time;
  
  return backendData;
};

const transformBackendToMobile = async (backendData, tenantContext = null) => {
  const mobileData = {
    id: backendData.id,
    user_id: backendData.employeeId,
    customer_id: backendData.companyId,
    leave_type_id: backendData.leaveTypeId,
    no_of_days: Number(backendData.totalDays),
    reason: backendData.reason,
    leave_status: backendData.status,
    from_date: backendData.startDate,
    to_date: backendData.endDate,
    createdAt: backendData.createdAt
  };

  // Handle leave session
  if (backendData.isHalfDay) {
    backendData.halfDayType = backendData.halfDayType ==='first_half'?'First Half':'Second Half';
    mobileData.leave_session = backendData.halfDayType || 'First Half';
  } else {
    mobileData.leave_session = 'Full Day';
  }

  // Get workflow status for detailed approval information
  let workflowStatus = null;
  if (tenantContext) {
    try {
      workflowStatus = await workflowInstanceService.getWorkflowStatus('leave', backendData.id, tenantContext);
    } catch (error) {
      console.log(`⚠️ Could not get workflow status for leave ${backendData.id}:`, error.message);
      // Continue without workflow status - this is not a critical error
    }
  }

  // Handle approval status with team manager support
  if (workflowStatus && workflowStatus.steps && workflowStatus.steps.length > 0) {
    // Find reporting manager step (Level 1)
    const managerStep = workflowStatus.steps.find(step => step.level === 1 && step.approverType === 'manager');
    // Find team manager step (Level 2)
    const teamManagerStep = workflowStatus.steps.find(step => step.level === 2 && step.approverType === 'manager');

    if (managerStep) {
      mobileData.approver_status = managerStep.status === 'completed' ?
        (managerStep.decision === 'approved' ? 'Approved' : 'Rejected') : 'Pending';
      mobileData.approver_id = managerStep.approverId;
      mobileData.approver_comment = managerStep.comments || '';
    }

    if (teamManagerStep) {
      mobileData.approver2_status = teamManagerStep.status === 'completed' ?
        (teamManagerStep.decision === 'approved' ? 'Approved' : 'Rejected') : 'Pending';
      mobileData.approver_id2 = teamManagerStep.approverId;
      mobileData.approver2_comment = teamManagerStep.comments || '';
    } else {
      // No team manager step
      mobileData.approver2_status = 'Not Required';
      mobileData.approver_id2 = null;
      mobileData.approver2_comment = '';
    }
  } else {
    // Fallback to basic status
    if (backendData.status === 'approved') {
      mobileData.approver_status = 'Approved';
      mobileData.approver2_status = 'Approved';
    } else if (backendData.status === 'rejected') {
      mobileData.approver_status = 'Rejected';
      mobileData.approver2_status = 'Rejected';
    } else {
      mobileData.approver_status = 'Pending';
      mobileData.approver2_status = 'Pending';
    }
  }
  
  // Add time fields for mobile compatibility
  mobileData.from_time = '00:00:00';
  mobileData.to_time = '23:59:59';
  
  // Include nested objects if available
  if (backendData.leaveType) {
    mobileData.LeaveType = {
      id: backendData.leaveType.id,
      leave_type_name: backendData.leaveType.name,
      leave_code: backendData.leaveType.code
    };
  } else if (backendData.leaveTypeId && tenantContext) {
    // Fallback: Fetch leave type name if not included in the association
    try {
      const leaveType = await LeaveType.findOne({
        where: {
          id: backendData.leaveTypeId,
          companyId: tenantContext.companyId
        },
        attributes: ['id', 'name', 'code']
      });

      if (leaveType) {
        mobileData.LeaveType = {
          id: leaveType.id,
          leave_type_name: leaveType.name,
          leave_code: leaveType.code
        };
      } else {
        mobileData.LeaveType = {
          leave_type_name: ""
        };
      }
    } catch (error) {
      console.error('Error fetching leave type:', error);
      mobileData.LeaveType = {
        leave_type_name: ""
      };
    }
  } else {
    mobileData.LeaveType = {
      leave_type_name: ""
    };
  }
  
  if (backendData.employee) {
    mobileData.User = {
      id: backendData.employee.id,
      firstName: backendData.employee.firstName,
      lastName: backendData.employee.lastName,
      email: backendData.employee.user?.email || '',
      profileImage: backendData.employee.profileImage || '',
      employeeId: backendData.employee.employeeId
    };
  }
  
  return mobileData;
};

/**
 * Get leave types with user balance data
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getLeaveTypes = async (req, res, next) => {
  try {
    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);
    
    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }
    
    // Get leave types with balance information
    const currentYear = new Date().getFullYear();
    const leaveTypesWithBalance = await leaveBalanceService.getEmployeeDetailedLeaveBalance(
      employee.id,
      currentYear,
      req.tenantContext
    );
    // Transform to mobile format
    let mobileLeaveTypes = [];
    console.log(leaveTypesWithBalance)
    if (Array.isArray(leaveTypesWithBalance)) {
      mobileLeaveTypes = leaveTypesWithBalance.map(balance => ({
        id: balance.id,
        user_id: employee.id,
        customer_id: req.tenantContext.companyId,
        leave_type_id: balance.id,
        allotted_leaves: balance.allocated,
        used_leave: balance.used,
        allotted_year: leaveTypesWithBalance.year,
        LeaveType: {
          id: balance.id,
          leave_type_name: balance.leaveType,
          leave_code: balance.leaveTypeCode
        }
      }));
    } else if (leaveTypesWithBalance && typeof leaveTypesWithBalance === 'object') {
      // Handle case where service returns an object with data property
      const balanceData = leaveTypesWithBalance.data || leaveTypesWithBalance.leaveBalances || [];
      if (Array.isArray(balanceData)) {
        mobileLeaveTypes = balanceData.map(balance => ({
          id: balance.id,
          user_id: employee.id,
          customer_id: req.tenantContext.companyId,
          leave_type_id: balance.id,
          allotted_leaves: balance.allocated ,
          used_leave: balance.used,
          allotted_year: leaveTypesWithBalance.year,
          LeaveType: {
            id: balance.id,
            leave_type_name: balance.leaveType,
            leave_code: balance.leaveTypeCode
          }
        }));
      }
    }
    
    return mobilePaginatedResponse(res, {
      message: 'Leave types retrieved successfully',
      data: mobileLeaveTypes,
      pagination: { total: mobileLeaveTypes.length }
    });
  } catch (error) {
    logger.error('Mobile get leave types error:', error);
    next(error);
  }
};

/**
 * Apply for leave
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const applyLeave = async (req, res, next) => {
  try {
    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Transform mobile request to backend format
    const backendData = transformMobileToBackend(req.body);
    backendData.employeeId = employee.id;

    // Create leave request
    await leaveService.createLeave(backendData, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: 'Leave application submitted successfully',
      result: {}
    }, 200);
  } catch (error) {
    logger.error('Mobile apply leave error:', error);
    next(error);
  }
};

/**
 * Get user's leave applications
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getLeaves = async (req, res, next) => {
  try {
    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);
console.log('-------------------------------',employee)
    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Get pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.size) || 10;
    const filters = {
      employeeId: employee.id,
      ...(req.query.leave_status && { status: req.query.leave_status.toLowerCase() })
    };
    
    // Get user's leave applications
    const leaveResults = await leaveService.getAllLeaves2(
      filters,
      { page, limit },
      { sortBy: 'createdAt', sortOrder: 'DESC' },
      req.tenantContext
    );

    // Transform to mobile format with workflow status
    const mobileLeaves = await Promise.all(
      leaveResults.map(async leave => await transformBackendToMobile(leave, req.tenantContext))
    );

    return mobilePaginatedResponse(res, {
      message: 'Leave applications retrieved successfully',
      data: mobileLeaves,
      pagination: { total: mobileLeaves.length }
    });
  } catch (error) {
    logger.error('Mobile get leaves error:', error);
    next(error);
  }
};

/**
 * Get leaves for approval (manager view)
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getEmployeeLeaves = async (req, res, next) => {
  try {
    let { page = 1, size = 10, leave_status = 'pending', search } = req.query;
    let limit = size;
    leave_status = leave_status.toLowerCase();

    let assignmentDecision = [];
    let approvalMobileStatus = null;
    if (leave_status === 'pending') {
      assignmentDecision = ['pending'];
      approvalMobileStatus = 'Pending';
    } else if (leave_status === 'approved') {
      assignmentDecision = ['approved'];
      approvalMobileStatus = 'Approved';
    } else if (leave_status === 'rejected') {
      assignmentDecision = ['rejected', 'cancelled'];
      approvalMobileStatus = 'Rejected';
    }
    
    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    const myAssignments = await WorkflowAssignment.findAll({
      where: {
        assigneeId: employee?.id,
        companyId: req?.tenantContext?.companyId
      },
      include: [
        {
          model: WorkflowInstance,
          as: 'instance',
          where: {
            entityType: 'leave',
          },
          required: true
        }
      ]
    });
    let myAssignmentsLeaveIds = myAssignments.map(assignment => assignment?.instance?.entityId);
    const myAssignmentsOverallApproved = await Leave.findAll({
      where: {
        id: {
          [Op.in]: myAssignmentsLeaveIds
        },
        status: 'approved'
      }
    });
    let myAssignmentsOverallApprovedIds = myAssignmentsOverallApproved.map(leave => leave?.id);

    const myAssignmentsOverallRejected = await Leave.findAll({
      where: {
        id: {
          [Op.in]: myAssignmentsLeaveIds
        },
        status: 'rejected'
      }
    });
    let myAssignmentsOverallRejectedIds = myAssignmentsOverallRejected.map(leave => leave?.id);


    const pendingAssignments = await WorkflowAssignment.findAll({
      where: {
        assigneeId: employee.id,
        decision: {
          [Op.in]: assignmentDecision
        },
        companyId: req.tenantContext.companyId
      },
      include: [
        {
          model: WorkflowInstance,
          as: 'instance',
          where: {
            entityType: 'leave',
          },
          required: true
        }
      ]
    });

    const assignedLeaveIds = pendingAssignments.map(assignment => assignment.instance.entityId);
    let leaveIdsSet = new Set([...assignedLeaveIds]);

    if (assignmentDecision[0] == 'pending') {
      for (let id of myAssignmentsOverallApprovedIds) {
        leaveIdsSet.delete(id);
      }
      for (let id of myAssignmentsOverallRejectedIds) {
        leaveIdsSet.delete(id);
      }
    }

    if (assignmentDecision[0] == 'approved') {
      for (let id of myAssignmentsOverallApprovedIds) {
        leaveIdsSet.add(id);
      }
    }

    if (assignmentDecision[0] == 'rejected') {
      for (let id of myAssignmentsOverallRejectedIds) {
        leaveIdsSet.add(id);
      }
    }

    let leaveIdsToInclude = [...leaveIdsSet];

    if (leaveIdsToInclude.length === 0) {
      return mobilePaginatedResponse(res, {
        message: 'No leave applications found for approval',
        data: [],
        pagination: { total: 0 }
      });
    }

    // Build filters for leave requests
    const filters = {};
    
    if (leaveIdsToInclude.length > 0) {
        filters.id = leaveIdsToInclude;
    }
    
    if (search) {
      filters.search = search;
    }
    // Add company filter
    filters.companyId = req.tenantContext.companyId;
    
    // Get leave applications
    let isMobile = true;
    const result = await leaveService.getAllLeaves(
      filters,
      { page: parseInt(page), limit: parseInt(limit) },
      { sortBy: 'createdAt', sortOrder: 'DESC' },
      req.tenantContext,
      isMobile
    );

    // Transform to mobile format with workflow status
    const mobileLeaves = await Promise.all(
      result.leaves.map(async leave => {
        let workflowStatus = null;
        try {
          workflowStatus = await workflowInstanceService.getWorkflowStatus('leave', leave.id, req.tenantContext);
        } catch (error) {
          console.error(`\n== Failed to get workflow status for Leave ID ${leave.id}:`, error);
        }

        // Get approval details from workflow status
        const approvals = [];
        approvals.push({
          id: null,
          approval_id: employee?.id,
          leave_request_id: leave.id,
          approval_comment: null,
          approval_status: approvalMobileStatus,
          created_by: leave.employeeId,
          modified_by: null,
          createdAt: leave.createdAt,
          updatedAt: leave.updatedAt,
          deletedAt: null
        });

        // Get employee details
        let employeeDetails = null;
        if (leave.employee) {
          employeeDetails = leave.employee;
        } else if (leave.employeeId) {
          employeeDetails = await Employee.findOne({
            where: { id: leave.employeeId }
          });
        }

        // Get leave type details
        let leaveType = null;
        if (leave.leaveTypeId) {
          leaveType = await LeaveType.findOne({
            where: { id: leave.leaveTypeId }
          });
        }

        // Format the response according to the required structure
        const mobileLeave = {
          id: leave?.id,
          user_id: leave?.employeeId,
          customer_id: leave?.companyId,
          leave_type_id: leave?.leaveTypeId || null,
          group_id: leave?.businessUnitId || null,
          no_of_days: parseFloat(leave?.totalDays),
          reason: leave?.reason || null,
          approver_id: employee?.id || leave?.approvedById || null,
          approver_id2: null,
          approver_comment: leave?.rejectionReason || null,
          approver2_comment: null,
          approver_status: approvalMobileStatus,
          approver2_status: 'Pending',
          leave_status: approvalMobileStatus,
          leave_session: leave?.isHalfDay ? leave?.halfDayType : 'Full Day',
          from_date: leave?.startDate,
          to_date: leave?.endDate,
          created_by: leave?.createdById || leave?.employeeId,
          modified_by: leave?.updatedById || null,
          appliedBy: true,
          createdAt: leave?.createdAt,
          updatedAt: leave?.updatedAt,
          deletedAt: null,
          LeavesRequestApprovals: approvals,
          User: employeeDetails ? {
            id: employeeDetails?.id,
            employee_id: employeeDetails?.employeeId || null,
            firstName: employeeDetails?.firstName,
            middleName: employeeDetails?.middleName || null,
            lastName: employeeDetails?.lastName,
            profile_image: employeeDetails?.profileImage || null,
          } : null,
          LeaveType: leaveType ? {
            id: leaveType.id,
            leave_type_name: leaveType.name,
            leave_code: leaveType.code
          } : null
        };

        return mobileLeave;
      })
    );

    return mobilePaginatedResponse(res, {
      message: 'Leave applications retrieved successfully',
      data: mobileLeaves,
      pagination: { total: result.total }
    });
  } catch (error) {
    logger.error('Mobile get employee leaves error:', error);
    next(error);
  }
};

/**
 * Update leave application
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateLeave = async (req, res, next) => {
  try {
    const { leaveId } = req.params;

    // Transform mobile request to backend format
    const backendData = transformMobileToBackend(req.body);

    // Update leave request
    await leaveService.updateLeave(leaveId, backendData, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: 'Leave application updated successfully',
      result: {}
    });
  } catch (error) {
    logger.error('Mobile update leave error:', error);
    next(error);
  }
};

/**
 * Withdraw leave application
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const withdrawLeave = async (req, res, next) => {
  try {
    const { leaveId } = req.params;

    // Cancel leave request
    await leaveService.cancelLeave(leaveId, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: 'Leave application withdrawn successfully',
      result: {}
    });
  } catch (error) {
    logger.error('Mobile withdraw leave error:', error);
    next(error);
  }
};

/**
 * Approve or reject leave application
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const approveRejectLeave = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, comment } = req.body;

    console.log(`🔍 DEBUG: Mobile approve/reject leave called for ID: ${id}, status: ${status}`);

    if (!['Approved', 'Rejected'].includes(status)) {
      throw new ValidationError('Invalid status. Must be "Approved" or "Rejected"');
    }

    // Use leave workflow service for approval/rejection with notifications
    if (status === 'Approved') {
      console.log(`🔍 DEBUG: Processing approval for leave ID: ${id}`);
      const approvalData = {
        comments: comment || '',
        actionBy: req.tenantContext.userId,
        actionDate: new Date()
      };
      console.log(`🔍 DEBUG: Calling leaveWorkflowService.processLeaveApproval with data:`, approvalData);
      await leaveWorkflowService.processLeaveApproval(id, approvalData, req.tenantContext);
      console.log(`🔍 DEBUG: leaveWorkflowService.processLeaveApproval completed successfully`);
    } else {
      console.log(`🔍 DEBUG: Processing rejection for leave ID: ${id}`);
      const rejectionData = {
        rejectionReason: comment || 'Leave rejected',
        actionBy: req.tenantContext.userId,
        actionDate: new Date()
      };
      console.log(`🔍 DEBUG: Calling leaveWorkflowService.processLeaveRejection with data:`, rejectionData);
      await leaveWorkflowService.processLeaveRejection(id, rejectionData, req.tenantContext);
      console.log(`🔍 DEBUG: leaveWorkflowService.processLeaveRejection completed successfully`);
    }

    return mobileSuccessResponse(res, {
      message: `Leave application ${status} successfully`,
      result: {}
    });
  } catch (error) {
    console.error(`🔍 DEBUG: Error in mobile approve/reject leave:`, error);
    logger.error('Mobile approve/reject leave error:', error);
    next(error);
  }
};

/**
 * Bulk approve or reject leave applications - USING NEW FLEXIBLE APPROVAL SYSTEM
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkApproveRejectLeaves = async (req, res, next) => {
  try {
    // Support both leaveId and leave_id for frontend compatibility
    const { leaveId, leave_id, status, comment, rejectionReason } = req.body;
    const leaveIds = leaveId || leave_id;

    if (!Array.isArray(leaveIds) || leaveIds.length === 0) {
      throw new ValidationError('leaveId or leave_id must be a non-empty array');
    }

    if (!['Approved', 'Rejected'].includes(status)) {
      throw new ValidationError('Invalid status. Must be "Approved" or "Rejected"');
    }

    console.log(`🔍 DEBUG: Mobile bulk ${status} for ${leaveIds.length} leave requests:`, leaveIds);

    // Process each leave application using NEW flexible approval pattern
    const results = [];
    const errors = [];
    
    for (const id of leaveIds) {
      try {
        console.log(`🔍 DEBUG: Processing mobile leave ${status} for ID: ${id}`);

        if (status === 'Approved') {
          const approvalData = {
            comments: comment || 'Mobile bulk approved',
            actionBy: req.tenantContext.userId,
            actionDate: new Date()
          };
          console.log(`🔍 DEBUG: Calling leaveWorkflowService.processLeaveApproval for mobile`);
          await leaveWorkflowService.processLeaveApproval(id, approvalData, req.tenantContext);
        } else {
          const rejectionData = {
            rejectionReason: rejectionReason || comment || 'Mobile bulk rejected',
            actionBy: req.tenantContext.userId,
            actionDate: new Date()
          };
          console.log(`🔍 DEBUG: Calling leaveWorkflowService.processLeaveRejection for mobile`);
          await leaveWorkflowService.processLeaveRejection(id, rejectionData, req.tenantContext);
        }

        results.push({
          id,
          success: true,
          message: `Leave ${status} successfully`
        });
        console.log(`✅ DEBUG: Mobile leave ${id} ${status} successfully`);
      } catch (error) {
        console.error(`❌ DEBUG: Error processing mobile leave ${id}:`, error.message);
        errors.push({
          id,
          success: false,
          error: error.message
        });
      }
    }

    return mobileSuccessResponse(res, {
      message: `Mobile bulk ${status} completed. ${results.length} successful, ${errors.length} failed.`,
      result: {
        successful: results,
        failed: errors,
        summary: {
          total: leaveIds.length,
          successful: results.length,
          failed: errors.length
        }
      }
    });
  } catch (error) {
    console.error(`🔍 DEBUG: Error in mobile bulk approve/reject leaves:`, error);
    logger.error('Mobile bulk approve/reject leaves error:', error);
    next(error);
  }
};

/**
 * Get leave request details by ID
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getLeaveDetails = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get leave request details
    const leaveRequest = await leaveService.getLeaveById(id, req.tenantContext);

    if (!leaveRequest) {
      return mobileSuccessResponse(res, {
        message: 'Leave not found',
        result: null
      });
    }

    // Get approval details using the flexible approval system (same as web)
    const approvalDetails = await approvalDetailsService.getUserApprovalDetails(
      'leave',
      parseInt(id),
      req.tenantContext
    );

    const empDetails = await Employee.findOne({
      where: { id: leaveRequest?.employeeId }
    });

    let newMappedData = [];
    if (approvalDetails && approvalDetails.hasWorkflow) {
      // Combine approvers and approval history from flexible approval system
      const allApprovers = [
        ...(approvalDetails.approvers || []),
        ...(approvalDetails.approvalHistory || [])
      ];

      if (allApprovers.length > 0) {
        newMappedData = allApprovers.map(approval => {
          const nameParts = approval?.approverName?.trim().split(/\s+/) || [];
          const tempFirstName = nameParts[0] || '';
          const tempMiddleName = nameParts.length > 2 ? nameParts.slice(1, -1).join(' ') : '';
          const tempLastName = nameParts.length > 1 ? nameParts[nameParts.length - 1] : '';
          let approvalRole = null;
          if (approval?.approverId == empDetails?.reportingManagerId) {
            approvalRole = 'Reporting Manager';
          } else if (approval?.approverId == empDetails?.teamManagerId) {
            approvalRole = 'Team Manager';

          } else {
            approvalRole = approval?.stepName?.replace(/approval/i, '').trim().toLowerCase().replace(/\b\w/g, char => char.toUpperCase()) || 'Approver';
            
          }
          return {
            approval_id: approval.approverId, // 🔑 Primary key (database ID)
            approval_status: approval.status === 'pending' ? 'Pending' :
                              approval.status === 'completed' ?
                                (approval.decision === 'approved' ? 'Approved' : 'Rejected') : 'Pending',
            createdAt: leaveRequest?.createdAt,
            updatedAt: leaveRequest?.updatedAt,
            User: {
              firstName: tempFirstName || '',
              middleName: tempMiddleName || '',
              lastName: tempLastName || '',
              employee_id: approval?.employeeId, // 🔑 Use same field as approval_id
              profile_image: approval?.profileImage
            },
            role: approvalRole
          }
        });
      }
    } else {
      console.log(`⚠️ No approval details found for leave ${id} - hasWorkflow: ${approvalDetails?.hasWorkflow}`);
    }

    // Transform to mobile format matching the required response structure
    const mobileLeaveData = {
      id: leaveRequest?.id,
      user_id: leaveRequest?.employeeId,
      customer_id: leaveRequest?.companyId,
      no_of_days: parseFloat(leaveRequest?.totalDays),
      from_date: leaveRequest?.startDate,
      to_date: leaveRequest?.endDate,
      reason: leaveRequest?.reason,
      leave_session: leaveRequest?.isHalfDay ? leaveRequest?.halfDayType : 'Full Day',
      createdAt: leaveRequest?.createdAt,
      LeaveType: {
        leave_type_name: leaveRequest?.leaveType?.name || ''
      },
      User: {
        id: leaveRequest?.employee.id,
        firstName: leaveRequest?.employee?.firstName || '',
        middleName: leaveRequest?.employee?.middleName || '',
        lastName: leaveRequest?.employee?.lastName || '',
        employee_id: leaveRequest?.employee?.employeeId || '',
        profileImage: leaveRequest?.employee?.profileImage || '',
      },
      LeavesRequestApprovals: newMappedData
    };

    return mobileSuccessResponse(res, {
      message: 'Leave fetched successfully',
      result: mobileLeaveData
    });
  } catch (error) {
    logger.error('Mobile get leave details error:', error);
    next(error);
  }
};

module.exports = {
  getLeaveTypes,
  applyLeave,
  getLeaves,
  getEmployeeLeaves,
  updateLeave,
  withdrawLeave,
  approveRejectLeave,
  bulkApproveRejectLeaves,
  getLeaveDetails,
  transformMobileToBackend,
  transformBackendToMobile
};
