'use strict';

const express = require('express');
const leaveController = require('../controllers/leave.controller');
const { validate, validateQuery, validateParams } = require('../middlewares/validation.middleware');
const leaveValidator = require('../validators/leave.validator');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission, hasRole } = require('../middlewares/authorization.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Leave type routes - MOVED UP to be processed before the /:id route
router.get(
  '/types',
  validateQuery(leaveValidator.getAllLeaveTypes),
  // hasPermission('leave:read'),
  leaveController.getAllLeaveTypes
);

router.post(
  '/types',
  validate(leaveValidator.createLeaveType),
  hasRole(['hr_manager', 'company_admin','admin']),
  // hasPermission('leave:manage'),
  leaveController.createLeaveType
);

router.get(
  '/types/:id',
  // hasPermission('leave:read'),
  leaveController.getLeaveTypeById
);

router.put(
  '/types/:id',
  validate(leaveValidator.updateLeaveType),
  hasRole(['hr_manager', 'company_admin','admin']),
  // hasPermission('leave:manage'),
  leaveController.updateLeaveType
);

router.delete(
  '/types/:id',
  hasRole(['hr_manager', 'company_admin','admin']),
  // hasPermission('leave:manage'),
  leaveController.deleteLeaveType
);

// Dashboard routes (must be before parameterized routes)
router.get(
  '/dashboard/summary',
  // hasPermission('leave:read'),
  leaveController.getDashboardSummary
);

router.get(
  '/dashboard/upcoming',
  validateQuery(leaveValidator.getUpcomingLeaves),
  hasPermission('leave:read'),
  leaveController.getUpcomingLeaves
);

router.get(
  '/dashboard/pending-approvals',
  validateQuery(leaveValidator.getPendingApprovals),
  hasPermission('leave:read'),
  leaveController.getPendingApprovals
);

// 🚀 NEW: Workflow routes (must be before parameterized routes)
router.get(
  '/workflow/pending-approvals',
  hasPermission('leave:read'),
  leaveController.getPendingApprovalsWithWorkflow
);

// Leave Reports routes (must be before parameterized routes)
router.get(
  '/reports/leave-usage',
  validateQuery(leaveValidator.getLeaveUsageReport),
  hasPermission('leave:read'),
  leaveController.getLeaveUsageReport
);

router.get(
  '/reports/wfh-usage',
  validateQuery(leaveValidator.getWfhUsageReport),
  hasPermission('leave:read'),
  leaveController.getWfhUsageReport
);

router.get(
  '/reports/leave-balance',
  validateQuery(leaveValidator.getLeaveBalanceReport),
  hasPermission('leave:read'),
  leaveController.getLeaveBalanceReport
);

router.get(
  '/reports/department-analysis',
  validateQuery(leaveValidator.getDepartmentAnalysisReport),
  hasPermission('leave:read'),
  leaveController.getDepartmentAnalysisReport
);

router.get(
  '/reports/export',
  validateQuery(leaveValidator.exportLeaveReports),
  hasPermission('leave:read'),
  leaveController.exportLeaveReports
);

// Get employee leave chart data
router.get(
  '/employee/:employeeId/chart',
  validateQuery(leaveValidator.getEmployeeLeaveChart),
  hasPermission('leave:read'),
  leaveController.getEmployeeLeaveChart
);

// Leave balance routes
router.get(
  '/employee/:employeeId/balance',
  validateQuery(leaveValidator.getEmployeeLeaveBalance),
  hasPermission('leave:read'),
  leaveController.getEmployeeLeaveBalance
);

router.get(
  '/employee/:employeeId/balance/detailed',
  validateQuery(leaveValidator.getEmployeeLeaveBalance),
  hasPermission('leave:read'),
  leaveController.getEmployeeDetailedLeaveBalance
);

router.put(
  '/employee/:employeeId/balance/detailed',
  validate(leaveValidator.updateEmployeeDetailedLeaveBalance),
  hasRole(['hr_manager', 'company_admin','admin']),
  hasPermission('leave:manage'),
  leaveController.updateEmployeeDetailedLeaveBalance
);

router.post(
  '/employee/:employeeId/leave-type/:leaveTypeId/adjust',
  validate(leaveValidator.adjustLeaveBalance),
  hasRole(['hr_manager', 'company_admin','admin']),
  hasPermission('leave:manage'),
  leaveController.adjustLeaveBalance
);

// Leave request routes
router.get(
  '/',
  validateQuery(leaveValidator.getAllLeaves),
  hasPermission('leave:read'),
  leaveController.getAllLeaves
);

router.post(
  '/',
  validate(leaveValidator.createLeave),
  hasPermission('leave:create'),
  leaveController.createLeave
);

// 🚀 NEW: Bulk operations routes - USING NEW FLEXIBLE APPROVAL SYSTEM (before parameterized routes)
router.post(
  '/bulk/approve',
  hasPermission('leave:approve'),
  leaveController.bulkApproveLeaves
);

router.post(
  '/bulk/reject',
  hasPermission('leave:approve'),
  leaveController.bulkRejectLeaves
);

// 🚀 NEW: Workflow status route (before parameterized routes)
router.get(
  '/:id/workflow',
  // hasPermission('leave:read'),
  leaveController.getLeaveWorkflowStatus
);

// These routes with :id parameter should be AFTER the more specific routes
router.get(
  '/:id',
  // hasPermission('leave:read'),
  leaveController.getLeaveById
);

router.put(
  '/:id',
  validate(leaveValidator.updateLeave),
  hasPermission('leave:update'),
  leaveController.updateLeave
);

router.delete(
  '/:id',
  hasPermission('leave:delete'),
  leaveController.deleteLeave
);

router.post(
  '/:id/approve',
  // hasPermission('leave:approve'),
  leaveController.approveLeave
);

router.post(
  '/:id/reject',
  validate(leaveValidator.rejectLeave),
  // hasPermission('leave:approve'),
  leaveController.rejectLeave
);

router.post(
  '/:id/cancel',
  hasPermission('leave:update'),
  leaveController.cancelLeave
);

module.exports = router;
